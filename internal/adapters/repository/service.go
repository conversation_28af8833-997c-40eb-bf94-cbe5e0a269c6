package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type ServiceRepository struct {
	db *gorm.DB
}

func NewServiceRepository(db *gorm.DB) ports.ServiceRepository {
	return &ServiceRepository{
		db: db,
	}
}

func (r *ServiceRepository) Insert(service *domain.Service) error {
	return r.db.Create(service).Error
}

func (r *ServiceRepository) FindAll(filter *ports.ServiceFilter) ([]*domain.Service, error) {
	var services []*domain.Service
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Preload("Namespace.Cluster.Workspace").
		Preload("Status")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name ILIKE ?", "%"+*filter.Name+"%")
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
	}

	err := query.Find(&services).Error
	return services, err
}

func (r *ServiceRepository) FindByID(id uint64) (*domain.Service, error) {
	var service domain.Service
	err := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Preload("Namespace.Cluster.Workspace").
		Preload("Deployment").
		Preload("IngressSpecs").
		Preload("Status").
		First(&service, id).Error
	if err != nil {
		return nil, err
	}
	return &service, nil
}

func (r *ServiceRepository) Update(service *domain.Service) error {
	result := r.db.Exec(
		"UPDATE service SET name = ?, port = ?, target_port = ?, type = ?, cluster_ip = ?, external_ip = ?, is_active = ?, namespace_id = ?, deployment_id = ?, status_id = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
		service.Name,
		service.Port,
		service.TargetPort,
		service.Type,
		service.ClusterIP,
		service.ExternalIP,
		service.IsActive,
		service.NamespaceID,
		service.DeploymentID,
		service.StatusID,
		service.UpdatedAt,
		service.ID,
	)

	return result.Error
}

func (r *ServiceRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Service{}, id).Error
}
