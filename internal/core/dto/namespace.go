package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateNamespaceRequest struct {
	Name      string               `json:"name" validate:"required,min=2,max=50"`
	Slug      string               `json:"slug" validate:"required,min=2,max=50"`
	IsActive  bool                 `json:"is_active"`
	Type      domain.NamespaceType `json:"type" validate:"required"`
	ClusterID uint64               `json:"cluster_id" validate:"required"`
}

type UpdateNamespaceRequest struct {
	Name      string               `json:"name" validate:"required,min=2,max=50"`
	Slug      string               `json:"slug" validate:"required,min=2,max=50"`
	IsActive  bool                 `json:"is_active"`
	Type      domain.NamespaceType `json:"type" validate:"required"`
	ClusterID uint64               `json:"cluster_id" validate:"required"`
}

type NamespaceListItemResponse struct {
	ID        uint64                   `json:"id"`
	CreatedAt time.Time                `json:"created_at"`
	UpdatedAt time.Time                `json:"updated_at"`
	Name      string                   `json:"name"`
	Slug      string                   `json:"slug"`
	IsActive  bool                     `json:"is_active"`
	Type      domain.NamespaceType     `json:"type"`
	Cluster   *ClusterRelationResponse `json:"cluster,omitempty"`
}

type NamespaceDetailResponse struct {
	ID          uint64                               `json:"id"`
	CreatedAt   time.Time                            `json:"created_at"`
	UpdatedAt   time.Time                            `json:"updated_at"`
	Name        string                               `json:"name"`
	Slug        string                               `json:"slug"`
	IsActive    bool                                 `json:"is_active"`
	Type        domain.NamespaceType                 `json:"type"`
	Cluster     *ClusterRelationResponse             `json:"cluster,omitempty"`
	Domains     []DomainDetailResponse               `json:"domains,omitempty"`
	Deployments []DeploymentWithEnvironmentsResponse `json:"deployments,omitempty"`
	Services    []ServiceWithIngressResponse         `json:"services,omitempty"`
	Ingress     []IngressWithIngressSpecsResponse    `json:"ingress,omitempty"`
}

type NamespaceRelationResponse struct {
	ID       uint64               `json:"id"`
	Name     string               `json:"name"`
	Slug     string               `json:"slug"`
	IsActive bool                 `json:"is_active"`
	Type     domain.NamespaceType `json:"type"`
}

// Convert response

func ToNamespaceListItemDTO(d *domain.Namespace) *NamespaceListItemResponse {
	if d == nil {
		return nil
	}

	return &NamespaceListItemResponse{
		ID:        d.ID,
		CreatedAt: d.CreatedAt,
		UpdatedAt: d.UpdatedAt,
		Name:      d.Name,
		Slug:      d.Slug,
		IsActive:  d.IsActive,
		Type:      d.Type,
		Cluster:   ToClusterRelationDTO(d.Cluster),
	}
}

func ToNamespaceDetailDTO(d *domain.Namespace) *NamespaceDetailResponse {
	if d == nil {
		return nil
	}

	resp := &NamespaceDetailResponse{
		ID:        d.ID,
		CreatedAt: d.CreatedAt,
		UpdatedAt: d.UpdatedAt,
		Name:      d.Name,
		Slug:      d.Slug,
		IsActive:  d.IsActive,
		Type:      d.Type,
		Cluster:   ToClusterRelationDTO(d.Cluster),
	}

	// Convert deployments with their environments
	if d.Deployments != nil {
		var deployments []DeploymentWithEnvironmentsResponse
		for _, deployment := range d.Deployments {
			deploymentResp := DeploymentWithEnvironmentsResponse{
				ID:            deployment.ID,
				Name:          deployment.Name,
				Image:         deployment.Image,
				ContainerPort: deployment.ContainerPort,
				Replicas:      deployment.Replicas,
				Status:        ToServerStatusRelationDTO(deployment.Status),
			}

			// Convert environments for this deployment
			if deployment.Environments != nil {
				var environments []EnvironmentRelationResponse
				for _, env := range deployment.Environments {
					environments = append(environments, EnvironmentRelationResponse{
						ID:    env.ID,
						Name:  env.Name,
						Value: env.Value,
					})
				}
				deploymentResp.Environments = environments
			}

			deployments = append(deployments, deploymentResp)
		}
		resp.Deployments = deployments
	}

	// Convert services with their ingress specs
	if d.Services != nil {
		var services []ServiceWithIngressResponse
		for _, service := range d.Services {
			serviceResp := ServiceWithIngressResponse{
				ID:         service.ID,
				Name:       service.Name,
				Port:       service.Port,
				TargetPort: service.TargetPort,
				Type:       service.Type,
				ClusterIP:  service.ClusterIP,
				ExternalIP: service.ExternalIP,
				IsActive:   service.IsActive,
				Status:     ToServerStatusRelationDTO(service.Status),
			}

			// Convert ingress specs for this service
			if service.IngressSpecs != nil {
				var ingressSpec []IngressSpecRelationResponse
				for _, ing := range service.IngressSpecs {
					ingressSpec = append(ingressSpec, IngressSpecRelationResponse{
						ID:   ing.ID,
						Host: ing.Host,
						Path: ing.Path,
						Port: ing.Port,
					})
				}
				serviceResp.IngressSpecs = ingressSpec
			}

			services = append(services, serviceResp)
		}
		resp.Services = services
	}

	// Convert ingress with their ingress specs
	if d.Ingress != nil {
		var ingress []IngressWithIngressSpecsResponse
		for _, ing := range d.Ingress {
			ingressResp := IngressWithIngressSpecsResponse{
				ID:     ing.ID,
				Name:   ing.Name,
				Class:  ing.Class,
				Status: ToServerStatusRelationDTO(ing.Status),
			}

			// Convert ingress specs for this ingress
			if ing.IngressSpecs != nil {
				var ingressSpecs []IngressSpecRelationResponse
				for _, spec := range ing.IngressSpecs {
					ingressSpecs = append(ingressSpecs, IngressSpecRelationResponse{
						ID:      spec.ID,
						Host:    spec.Host,
						Path:    spec.Path,
						Port:    spec.Port,
						Service: ToServiceRelationDTO(spec.Service),
					})
				}
				ingressResp.IngressSpecs = ingressSpecs
			}

			ingress = append(ingress, ingressResp)
		}
		resp.Ingress = ingress
	}

	if d.Domains != nil {
		var domains []DomainDetailResponse
		for _, dm := range d.Domains {
			domains = append(domains, *ToDomainDetailDTO(dm))
		}
		resp.Domains = domains
	}

	return resp
}

func ToNamespaceRelationDTO(d *domain.Namespace) *NamespaceRelationResponse {
	if d == nil {
		return nil
	}
	return &NamespaceRelationResponse{
		ID:       d.ID,
		Name:     d.Name,
		Slug:     d.Slug,
		IsActive: d.IsActive,
		Type:     d.Type,
	}
}
