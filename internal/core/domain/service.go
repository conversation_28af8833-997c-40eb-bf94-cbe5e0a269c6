package domain

type Service struct {
	BaseModel
	Name         string `json:"name" gorm:"not null"`
	Port         string `json:"port" gorm:"not null"`
	TargetPort   string `json:"target_port" gorm:"not null"`
	Type         string `json:"type" gorm:"not null default:ClusterIP"` // e.g., ClusterIP, NodePort, LoadBalancer
	ClusterIP    string `json:"cluster_ip"`
	ExternalIP   string `json:"external_ip"`
	IsActive     bool   `json:"is_active" gorm:"not null default:true"`
	NamespaceID  uint64 `json:"namespace_id" gorm:"not null"`
	DeploymentID uint64 `json:"deployment_id" gorm:"not null"`
	StatusID     uint64 `json:"status_id" gorm:"not null"`

	// Relationships
	Namespace    *Namespace     `json:"namespace,omitempty" gorm:"foreignKey:NamespaceID"`
	Deployment   *Deployment    `json:"deployment,omitempty" gorm:"foreignKey:DeploymentID"`
	Status       *ServerStatus  `json:"status,omitempty" gorm:"foreignKey:StatusID"`
	IngressSpecs []*IngressSpec `json:"ingress_specs,omitempty" gorm:"foreignKey:ServiceID"`
}
