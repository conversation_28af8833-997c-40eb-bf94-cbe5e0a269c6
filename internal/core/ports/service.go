package ports

import "ops-api/internal/core/domain"

// ServiceFilter represents filters for querying services
type ServiceFilter struct {
	Name        *string
	NamespaceID *uint64
	IsActive    *bool
}

type ServiceRepository interface {
	Insert(service *domain.Service) error
	FindAll(filter *ServiceFilter) ([]*domain.Service, error)
	FindByID(id uint64) (*domain.Service, error)
	Update(service *domain.Service) error
	Delete(id uint64) error
}

type ServiceService interface {
	Create(name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID uint64) (*domain.Service, error)
	GetAll(filter *ServiceFilter) ([]*domain.Service, error)
	GetByID(id uint64) (*domain.Service, error)
	Update(id uint64, name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID, statusID uint64) (*domain.Service, error)
	UpdateStatus(id uint64, statusID uint64) (*domain.Service, error)
	UpdateActiveStatus(id uint64, isActive bool) (*domain.Service, error)
	Delete(id uint64) error
}
